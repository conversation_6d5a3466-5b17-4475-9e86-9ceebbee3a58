#!/usr/bin/env python3
"""
简单测试脚本
使用项目自带的示例文件进行测试
"""

import os
import subprocess
import sys
from loguru import logger

def run_generation_example(transcript, ref_audio=None, output_name="test", extra_args=None):
    """运行generation.py示例"""
    
    output_dir = "./simple_test_outputs"
    os.makedirs(output_dir, exist_ok=True)
    
    output_file = os.path.join(output_dir, f"{output_name}.wav")
    
    # 基础命令
    cmd = [
        "./conda_env/bin/python", "examples/generation.py",
        "--transcript", transcript,
        "--temperature", "0.5",
        "--device", "cpu",
        "--out_path", output_file
    ]
    
    # 添加参考音频
    if ref_audio:
        cmd.extend(["--ref_audio", ref_audio])
    
    # 添加额外参数
    if extra_args:
        cmd.extend(extra_args)
    
    logger.info(f"运行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            logger.info(f"✅ 生成成功: {output_file}")
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                logger.info(f"📁 文件大小: {file_size} bytes")
            return True
        else:
            logger.error(f"❌ 生成失败:")
            logger.error(f"stdout: {result.stdout}")
            logger.error(f"stderr: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("⏰ 生成超时")
        return False
    except Exception as e:
        logger.error(f"💥 生成出错: {e}")
        return False

def main():
    """主测试函数"""
    
    logger.info("🚀 开始简单测试...")
    
    test_results = []
    
    # 测试1: 基础英文语音合成
    logger.info("\n1️⃣ 测试基础英文语音合成...")
    success = run_generation_example(
        transcript="examples/transcript/single_speaker/en_basic.txt",
        output_name="en_basic"
    )
    test_results.append(("基础英文语音合成", success))
    
    # 测试2: 中文语音合成
    logger.info("\n2️⃣ 测试中文语音合成...")
    success = run_generation_example(
        transcript="examples/transcript/single_speaker/zh_ai.txt",
        output_name="zh_ai"
    )
    test_results.append(("中文语音合成", success))
    
    # 测试3: 英文声音克隆
    logger.info("\n3️⃣ 测试英文声音克隆 (belinda)...")
    success = run_generation_example(
        transcript="examples/transcript/single_speaker/en_basic.txt",
        ref_audio="belinda",
        output_name="en_voice_clone_belinda"
    )
    test_results.append(("英文声音克隆", success))
    
    # 测试4: 多人对话
    logger.info("\n4️⃣ 测试多人对话...")
    success = run_generation_example(
        transcript="examples/transcript/multi_speaker/en_argument.txt",
        output_name="multi_speaker_argument",
        extra_args=["--chunk_method", "speaker"]
    )
    test_results.append(("多人对话", success))
    
    # 测试5: 多人对话 + 声音克隆
    logger.info("\n5️⃣ 测试多人对话 + 声音克隆...")
    success = run_generation_example(
        transcript="examples/transcript/multi_speaker/en_argument.txt",
        ref_audio="belinda,chadwick",
        output_name="multi_speaker_with_voices",
        extra_args=["--chunk_method", "speaker", "--ref_audio_in_system_message"]
    )
    test_results.append(("多人对话+声音克隆", success))
    
    # 汇总结果
    logger.info("\n" + "="*50)
    logger.info("📊 测试结果汇总")
    logger.info("="*50)
    
    success_count = 0
    for test_name, success in test_results:
        status = "✅ 成功" if success else "❌ 失败"
        logger.info(f"{status} - {test_name}")
        if success:
            success_count += 1
    
    total_tests = len(test_results)
    success_rate = success_count / total_tests * 100
    
    logger.info(f"\n📈 总体结果:")
    logger.info(f"成功: {success_count}/{total_tests}")
    logger.info(f"成功率: {success_rate:.1f}%")
    
    if success_count > 0:
        logger.info("🎉 至少有部分测试成功！")
        logger.info("📁 音频文件保存在: ./simple_test_outputs/")
        return True
    else:
        logger.error("❌ 所有测试都失败了")
        return False

if __name__ == "__main__":
    logger.info("⚡ 开始Higgs Audio V2简单功能测试...")
    
    success = main()
    
    if success:
        logger.info("🎊 简单测试完成！")
    else:
        logger.error("💥 简单测试失败！")
        sys.exit(1)
