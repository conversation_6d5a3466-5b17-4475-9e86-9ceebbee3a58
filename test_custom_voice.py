#!/usr/bin/env python3
"""
自定义语音克隆测试脚本
使用新添加的zh_vo_Main_Linaxita_2_3_35_6音频进行语音克隆测试
"""

import os
import sys
import subprocess
from loguru import logger

def test_custom_voice_cloning():
    """测试自定义语音克隆功能"""
    
    # 创建输出目录
    output_dir = "./custom_voice_outputs"
    os.makedirs(output_dir, exist_ok=True)
    
    logger.info("🎭 开始自定义语音克隆测试...")
    
    # 你的自定义声音名称（不包含.wav扩展名）
    custom_voice_name = "zh_vo_Main_Linaxita_2_3_35_6"
    
    # 检查文件是否存在
    audio_file = f"examples/voice_prompts/{custom_voice_name}.wav"
    text_file = f"examples/voice_prompts/{custom_voice_name}.txt"
    
    if not os.path.exists(audio_file):
        logger.error(f"❌ 音频文件不存在: {audio_file}")
        return False
    
    if not os.path.exists(text_file):
        logger.error(f"❌ 文本文件不存在: {text_file}")
        return False
    
    # 读取参考文本
    with open(text_file, 'r', encoding='utf-8') as f:
        reference_text = f.read().strip()
    
    logger.info(f"✅ 找到音频文件: {audio_file}")
    logger.info(f"✅ 找到文本文件: {text_file}")
    logger.info(f"📝 参考文本: {reference_text}")
    
    # 测试文本列表
    test_texts = [
        "你好，这是使用你的声音进行语音克隆的测试。",
        "人工智能技术让语音合成变得越来越真实。",
        "今天天气很好，阳光明媚，心情也很不错。",
        "中文语音合成技术正在快速发展，效果越来越自然。",
        "希望这个语音克隆效果能够满足你的需求。"
    ]
    
    success_count = 0
    
    for i, text in enumerate(test_texts):
        logger.info(f"\n🎯 测试 {i+1}/5: {text}")
        
        output_file = os.path.join(output_dir, f"custom_voice_test_{i+1}.wav")
        
        # 构建命令
        cmd = [
            "./conda_env/bin/python", "examples/generation.py",
            "--model_path", "./models/bosonai/higgs-audio-v2-generation-3B-base",
            "--audio_tokenizer", "./models/bosonai/higgs-audio-v2-tokenizer",
            "--transcript", text,
            "--ref_audio", custom_voice_name,
            "--temperature", "0.3",  # 使用较低温度保持稳定性
            "--device", "cuda",
            "--out_path", output_file
        ]
        
        try:
            logger.info("🔄 正在生成语音...")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                file_size = os.path.getsize(output_file) if os.path.exists(output_file) else 0
                logger.info(f"✅ 生成成功: {output_file} ({file_size} bytes)")
                success_count += 1
            else:
                logger.error(f"❌ 生成失败:")
                logger.error(f"stderr: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ 生成超时")
        except Exception as e:
            logger.error(f"💥 生成出错: {e}")
    
    # 测试结果统计
    total_tests = len(test_texts)
    logger.info(f"\n" + "="*60)
    logger.info(f"📊 自定义语音克隆测试结果:")
    logger.info(f"成功: {success_count}/{total_tests}")
    logger.info(f"成功率: {success_count/total_tests*100:.1f}%")
    
    if success_count > 0:
        logger.info(f"✅ 自定义语音克隆测试完成！")
        logger.info(f"📁 音频文件保存在: {output_dir}")
        return True
    else:
        logger.error("❌ 所有自定义语音克隆测试都失败了")
        return False

def test_different_temperatures():
    """测试不同温度参数的效果"""
    
    logger.info("\n🌡️ 开始不同温度参数测试...")
    
    output_dir = "./custom_voice_outputs"
    os.makedirs(output_dir, exist_ok=True)
    
    custom_voice_name = "zh_vo_Main_Linaxita_2_3_35_6"
    test_text = "这是一个测试不同温度参数对语音生成效果影响的实验。"
    
    # 不同的温度参数
    temperatures = [0.2, 0.5, 0.8]
    
    success_count = 0
    
    for temp in temperatures:
        logger.info(f"\n🌡️ 测试温度参数: {temp}")
        
        output_file = os.path.join(output_dir, f"custom_voice_temp_{temp}.wav")
        
        cmd = [
            "./conda_env/bin/python", "examples/generation.py",
            "--model_path", "./models/bosonai/higgs-audio-v2-generation-3B-base",
            "--audio_tokenizer", "./models/bosonai/higgs-audio-v2-tokenizer",
            "--transcript", test_text,
            "--ref_audio", custom_voice_name,
            "--temperature", str(temp),
            "--device", "cuda",
            "--out_path", output_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                file_size = os.path.getsize(output_file) if os.path.exists(output_file) else 0
                logger.info(f"✅ 温度{temp} 生成成功: {output_file} ({file_size} bytes)")
                success_count += 1
            else:
                logger.error(f"❌ 温度{temp} 生成失败: {result.stderr}")
                
        except Exception as e:
            logger.error(f"💥 温度{temp} 生成出错: {e}")
    
    logger.info(f"\n温度参数测试完成，成功: {success_count}/{len(temperatures)}")
    return success_count > 0

if __name__ == "__main__":
    logger.info("🚀 开始自定义语音克隆测试...")
    logger.info(f"🎵 使用音频: zh_vo_Main_Linaxita_2_3_35_6.wav")
    
    # 基础语音克隆测试
    basic_success = test_custom_voice_cloning()
    
    # 不同温度参数测试
    temp_success = test_different_temperatures()
    
    if basic_success or temp_success:
        logger.info("\n🎉 自定义语音克隆测试完成！")
        logger.info("📁 所有音频文件保存在: ./custom_voice_outputs/")
        logger.info("\n💡 提示:")
        logger.info("- 可以播放生成的音频文件来评估克隆效果")
        logger.info("- 温度参数越低(0.2)越稳定，越高(0.8)越有变化")
        logger.info("- 建议使用温度0.3-0.5获得最佳效果")
    else:
        logger.error("\n💥 自定义语音克隆测试失败！")
        sys.exit(1)
