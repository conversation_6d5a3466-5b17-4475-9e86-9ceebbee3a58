#!/usr/bin/env python3
"""
快速测试脚本
使用项目自带的generation.py进行快速功能测试
"""

import os
import subprocess
import sys
from loguru import logger

def quick_test():
    """快速测试基本功能"""
    
    logger.info("🚀 开始快速测试...")
    
    # 创建输出目录
    output_dir = "./quick_test_outputs"
    os.makedirs(output_dir, exist_ok=True)
    
    # 测试1: 基础语音合成（无参考声音）
    logger.info("\n1️⃣ 测试基础语音合成...")
    test_text = "Hello, this is a quick test of Higgs Audio V2 text-to-speech functionality."
    output_file = os.path.join(output_dir, "basic_test.wav")
    
    cmd = [
        "./conda_env/bin/python", "examples/generation.py",
        "--transcript", test_text,
        "--temperature", "0.5",
        "--device", "cpu",
        "--out_path", output_file
    ]
    
    try:
        logger.info("正在生成语音...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            logger.info(f"✅ 基础语音合成成功: {output_file}")
            basic_success = True
        else:
            logger.error(f"❌ 基础语音合成失败:")
            logger.error(result.stderr)
            basic_success = False
            
    except subprocess.TimeoutExpired:
        logger.error("⏰ 基础语音合成超时")
        basic_success = False
    except Exception as e:
        logger.error(f"💥 基础语音合成出错: {e}")
        basic_success = False
    
    # 测试2: 声音克隆（使用belinda声音）
    logger.info("\n2️⃣ 测试声音克隆...")
    clone_text = "This is a voice cloning test using the Belinda reference voice."
    clone_output = os.path.join(output_dir, "voice_clone_test.wav")
    
    cmd = [
        "./conda_env/bin/python", "examples/generation.py",
        "--transcript", clone_text,
        "--ref_audio", "belinda",
        "--temperature", "0.3",
        "--device", "cpu",
        "--out_path", clone_output
    ]
    
    try:
        logger.info("正在进行声音克隆...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            logger.info(f"✅ 声音克隆成功: {clone_output}")
            clone_success = True
        else:
            logger.error(f"❌ 声音克隆失败:")
            logger.error(result.stderr)
            clone_success = False
            
    except subprocess.TimeoutExpired:
        logger.error("⏰ 声音克隆超时")
        clone_success = False
    except Exception as e:
        logger.error(f"💥 声音克隆出错: {e}")
        clone_success = False
    
    # 测试3: 中文语音合成
    logger.info("\n3️⃣ 测试中文语音合成...")
    chinese_text = "你好，这是一个中文语音合成的快速测试。"
    chinese_output = os.path.join(output_dir, "chinese_test.wav")
    
    cmd = [
        "./conda_env/bin/python", "examples/generation.py",
        "--transcript", chinese_text,
        "--temperature", "0.4",
        "--device", "cpu",
        "--out_path", chinese_output
    ]
    
    try:
        logger.info("正在生成中文语音...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            logger.info(f"✅ 中文语音合成成功: {chinese_output}")
            chinese_success = True
        else:
            logger.error(f"❌ 中文语音合成失败:")
            logger.error(result.stderr)
            chinese_success = False
            
    except subprocess.TimeoutExpired:
        logger.error("⏰ 中文语音合成超时")
        chinese_success = False
    except Exception as e:
        logger.error(f"💥 中文语音合成出错: {e}")
        chinese_success = False
    
    # 测试4: 简单多人对话
    logger.info("\n4️⃣ 测试多人对话...")
    dialog_text = """[SPEAKER0] Hello, how are you?
[SPEAKER1] I'm fine, thank you! How about you?
[SPEAKER0] I'm doing great, thanks for asking."""
    
    dialog_output = os.path.join(output_dir, "dialog_test.wav")
    
    cmd = [
        "./conda_env/bin/python", "examples/generation.py",
        "--transcript", dialog_text,
        "--chunk_method", "speaker",
        "--temperature", "0.5",
        "--device", "cpu",
        "--out_path", dialog_output
    ]
    
    try:
        logger.info("正在生成多人对话...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=400)
        
        if result.returncode == 0:
            logger.info(f"✅ 多人对话成功: {dialog_output}")
            dialog_success = True
        else:
            logger.error(f"❌ 多人对话失败:")
            logger.error(result.stderr)
            dialog_success = False
            
    except subprocess.TimeoutExpired:
        logger.error("⏰ 多人对话超时")
        dialog_success = False
    except Exception as e:
        logger.error(f"💥 多人对话出错: {e}")
        dialog_success = False
    
    # 汇总结果
    results = [basic_success, clone_success, chinese_success, dialog_success]
    success_count = sum(results)
    total_tests = len(results)
    
    logger.info(f"\n📊 快速测试结果:")
    logger.info(f"✅ 基础语音合成: {'成功' if basic_success else '失败'}")
    logger.info(f"🎭 声音克隆: {'成功' if clone_success else '失败'}")
    logger.info(f"🇨🇳 中文语音合成: {'成功' if chinese_success else '失败'}")
    logger.info(f"👥 多人对话: {'成功' if dialog_success else '失败'}")
    logger.info(f"\n总成功率: {success_count}/{total_tests} ({success_count/total_tests*100:.1f}%)")
    
    if success_count > 0:
        logger.info(f"🎉 快速测试完成！音频文件保存在: {output_dir}")
        return True
    else:
        logger.error("❌ 所有快速测试都失败了")
        return False

if __name__ == "__main__":
    logger.info("⚡ 开始Higgs Audio V2快速功能测试...")
    
    success = quick_test()
    
    if success:
        logger.info("🎊 快速测试成功完成！")
    else:
        logger.error("💥 快速测试失败！")
        sys.exit(1)
