#!/usr/bin/env python3
"""
多人对话测试脚本
测试Higgs Audio V2的多人对话生成功能
"""

import os
import sys
import subprocess
from loguru import logger

def test_multi_speaker_dialog():
    """测试多人对话功能"""
    
    # 创建输出目录
    output_dir = "./multi_speaker_outputs"
    os.makedirs(output_dir, exist_ok=True)
    
    logger.info("开始多人对话测试...")
    
    # 测试用例1: 简单对话
    simple_dialog = """[SPEAKER0] Good morning! How are you today?
[SPEAKER1] Good morning! I'm doing well, thank you. How about you?
[SPEAKER0] I'm great! The weather is beautiful today.
[SPEAKER1] Yes, it's perfect for a walk in the park."""
    
    # 测试用例2: 商务对话
    business_dialog = """[SPEAKER0] Welcome to our company. I'm glad you could make it to the interview.
[SPEAKER1] Thank you for having me. I'm excited about this opportunity.
[SPEAKER0] Let's start with you telling me about your background and experience.
[SPEAKER1] I have five years of experience in software development, specializing in AI and machine learning."""
    
    # 测试用例3: 朋友聊天
    casual_dialog = """[SPEAKER0] Hey, did you watch the game last night?
[SPEAKER1] Oh yeah! It was incredible. That last-minute goal was amazing!
[SPEAKER0] I know, right? I couldn't believe it. The crowd went wild!
[SPEAKER1] We should definitely go to the next game together."""
    
    test_cases = [
        ("simple_dialog", simple_dialog, "简单对话"),
        ("business_dialog", business_dialog, "商务对话"),
        ("casual_dialog", casual_dialog, "朋友聊天")
    ]
    
    success_count = 0
    
    # 测试自动声音分配
    logger.info("\n=== 测试自动声音分配 ===")
    for case_name, dialog_text, description in test_cases:
        logger.info(f"\n测试 {description}...")
        
        output_file = os.path.join(output_dir, f"{case_name}_auto_voice.wav")
        
        cmd = [
            "./conda_env/bin/python", "examples/generation.py",
            "--transcript", dialog_text,
            "--chunk_method", "speaker",
            "--temperature", "0.5",
            "--device", "cpu",
            "--out_path", output_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                logger.info(f"✅ 自动声音分配成功: {output_file}")
                success_count += 1
            else:
                logger.error(f"❌ 自动声音分配失败: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ 生成超时")
        except Exception as e:
            logger.error(f"💥 生成出错: {e}")
    
    # 测试指定声音的对话
    logger.info("\n=== 测试指定声音对话 ===")
    voice_pairs = [
        ("belinda", "chadwick", "女性+男性"),
        ("en_woman", "en_man", "标准女声+标准男声"),
        ("mabel", "broom_salesman", "Mabel+销售员")
    ]
    
    for i, (voice1, voice2, description) in enumerate(voice_pairs):
        logger.info(f"\n测试声音组合: {description}")
        
        output_file = os.path.join(output_dir, f"specified_voices_{i+1}_{voice1}_{voice2}.wav")
        
        cmd = [
            "./conda_env/bin/python", "examples/generation.py",
            "--transcript", simple_dialog,  # 使用简单对话作为测试
            "--ref_audio", f"{voice1},{voice2}",
            "--ref_audio_in_system_message",
            "--chunk_method", "speaker",
            "--temperature", "0.4",
            "--device", "cpu",
            "--out_path", output_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                logger.info(f"✅ 指定声音对话成功: {output_file}")
                success_count += 1
            else:
                logger.error(f"❌ 指定声音对话失败: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ 生成超时")
        except Exception as e:
            logger.error(f"💥 生成出错: {e}")
    
    return success_count

def test_existing_multi_speaker_examples():
    """测试项目自带的多人对话示例"""
    
    logger.info("\n=== 测试项目自带的多人对话示例 ===")
    
    output_dir = "./multi_speaker_outputs"
    os.makedirs(output_dir, exist_ok=True)
    
    # 检查是否有现成的多人对话文本文件
    example_files = []
    transcript_dir = "examples/transcript/multi_speaker"
    
    if os.path.exists(transcript_dir):
        for file in os.listdir(transcript_dir):
            if file.endswith('.txt'):
                example_files.append(os.path.join(transcript_dir, file))
    
    if not example_files:
        logger.warning("没有找到多人对话示例文件")
        return 0
    
    success_count = 0
    
    for example_file in example_files:
        file_name = os.path.basename(example_file).replace('.txt', '')
        logger.info(f"\n测试示例文件: {file_name}")
        
        output_file = os.path.join(output_dir, f"example_{file_name}.wav")
        
        cmd = [
            "./conda_env/bin/python", "examples/generation.py",
            "--transcript", example_file,
            "--chunk_method", "speaker",
            "--temperature", "0.5",
            "--device", "cpu",
            "--out_path", output_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                logger.info(f"✅ 示例文件处理成功: {output_file}")
                success_count += 1
            else:
                logger.error(f"❌ 示例文件处理失败: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ 生成超时")
        except Exception as e:
            logger.error(f"💥 生成出错: {e}")
    
    return success_count

if __name__ == "__main__":
    logger.info("👥 开始多人对话测试...")
    
    # 测试多人对话功能
    dialog_success = test_multi_speaker_dialog()
    
    # 测试项目自带示例
    example_success = test_existing_multi_speaker_examples()
    
    total_success = dialog_success + example_success
    
    logger.info(f"\n📊 多人对话测试结果:")
    logger.info(f"总成功数: {total_success}")
    
    if total_success > 0:
        logger.info("🎉 多人对话测试完成！")
        logger.info("✅ 音频文件保存在: ./multi_speaker_outputs")
    else:
        logger.error("💥 所有多人对话测试都失败了！")
        sys.exit(1)
