#!/usr/bin/env python3
"""
基础语音合成测试脚本
测试Higgs Audio V2的基本文本转语音功能
"""

import os
import sys
import torch
import torchaudio
from loguru import logger
from boson_multimodal.serve.serve_engine import HiggsAudioServeEngine, HiggsAudioResponse
from boson_multimodal.data_types import ChatMLSample, Message

def test_basic_tts():
    """测试基础的文本转语音功能"""
    
    # 使用本地模型路径
    model_path = "./models/bosonai/higgs-audio-v2-generation-3B-base"
    tokenizer_path = "./models/bosonai/higgs-audio-v2-tokenizer"

    logger.info(f"使用本地模型: {model_path}")
    logger.info(f"使用本地tokenizer: {tokenizer_path}")
    
    # 设置设备
    device = "cuda" if torch.cuda.is_available() else "cpu"
    logger.info(f"使用设备: {device}")
    
    # 创建输出目录
    output_dir = "./basic_tts_outputs"
    os.makedirs(output_dir, exist_ok=True)
    
    # 测试文本列表
    test_texts = [
        "Hello, this is a basic text-to-speech test using Higgs Audio V2.",
        "The sun rises in the east and sets in the west. This simple fact has been observed by humans for thousands of years.",
        "Artificial intelligence is transforming the way we live and work.",
        "Welcome to the future of audio generation technology.",
    ]
    
    try:
        # 初始化服务引擎
        logger.info("正在初始化Higgs Audio服务引擎...")
        serve_engine = HiggsAudioServeEngine(model_path, tokenizer_path, device=device)
        logger.info("服务引擎初始化完成")
        
        # 系统提示
        system_prompt = (
            "Generate audio following instruction.\n\n<|scene_desc_start|>\nAudio is recorded from a quiet room.\n<|scene_desc_end|>"
        )
        
        # 对每个测试文本进行语音合成
        for i, text in enumerate(test_texts):
            logger.info(f"\n正在处理第 {i+1} 个测试文本: {text}")
            
            # 构建消息
            messages = [
                Message(role="system", content=system_prompt),
                Message(role="user", content=text),
            ]
            
            # 生成语音
            output: HiggsAudioResponse = serve_engine.generate(
                chat_ml_sample=ChatMLSample(messages=messages),
                max_new_tokens=1024,
                temperature=0.3,
                top_p=0.95,
                top_k=50,
                stop_strings=["<|end_of_text|>", "<|eot_id|>"],
            )
            
            # 保存音频文件
            output_path = os.path.join(output_dir, f"basic_test_{i+1}.wav")
            torchaudio.save(output_path, torch.from_numpy(output.audio)[None, :], output.sampling_rate)
            logger.info(f"音频已保存到: {output_path}")
            
        logger.info(f"\n✅ 基础语音合成测试完成！所有音频文件保存在: {output_dir}")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中出现错误: {e}")
        return False
    
    return True

if __name__ == "__main__":
    logger.info("开始基础语音合成测试...")
    success = test_basic_tts()
    
    if success:
        logger.info("🎉 基础语音合成测试成功完成！")
    else:
        logger.error("💥 基础语音合成测试失败！")
        sys.exit(1)
