#!/usr/bin/env python3
"""
声音克隆测试脚本
测试Higgs Audio V2的零样本声音克隆功能
"""

import os
import sys
import subprocess
from loguru import logger

def test_voice_cloning():
    """测试声音克隆功能"""
    
    # 创建输出目录
    output_dir = "./voice_cloning_outputs"
    os.makedirs(output_dir, exist_ok=True)
    
    # 测试文本
    test_text = "This is a voice cloning test. The model should generate speech that sounds similar to the reference voice."
    
    # 可用的参考声音列表
    available_voices = [
        "belinda",
        "chadwick", 
        "en_man",
        "en_woman",
        "broom_salesman",
        "mabel"
    ]
    
    # 测试参数
    test_configs = [
        {"temperature": 0.3, "description": "低温度(更稳定)"},
        {"temperature": 0.7, "description": "中等温度(平衡)"},
        {"temperature": 1.0, "description": "高温度(更多样)"}
    ]
    
    logger.info("开始声音克隆测试...")
    
    success_count = 0
    total_tests = len(available_voices) * len(test_configs)
    
    for voice in available_voices:
        logger.info(f"\n测试参考声音: {voice}")
        
        for config in test_configs:
            temp = config["temperature"]
            desc = config["description"]
            
            output_file = os.path.join(output_dir, f"voice_clone_{voice}_temp_{temp}.wav")
            
            # 构建命令
            cmd = [
                "./conda_env/bin/python", "examples/generation.py",
                "--transcript", test_text,
                "--ref_audio", voice,
                "--temperature", str(temp),
                "--device", "none",  # 强制使用CPU以避免CUDA问题
                "--out_path", output_file
            ]
            
            try:
                logger.info(f"  生成 {desc} 语音...")
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    logger.info(f"  ✅ 成功生成: {output_file}")
                    success_count += 1
                else:
                    logger.error(f"  ❌ 生成失败: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                logger.error(f"  ⏰ 生成超时")
            except Exception as e:
                logger.error(f"  💥 生成出错: {e}")
    
    # 测试结果统计
    logger.info(f"\n📊 声音克隆测试结果:")
    logger.info(f"成功: {success_count}/{total_tests}")
    logger.info(f"成功率: {success_count/total_tests*100:.1f}%")
    
    if success_count > 0:
        logger.info(f"✅ 声音克隆测试完成！音频文件保存在: {output_dir}")
        return True
    else:
        logger.error("❌ 所有声音克隆测试都失败了")
        return False

def test_multi_voice_cloning():
    """测试多声音克隆（对话场景）"""
    
    logger.info("\n开始多声音克隆测试...")
    
    output_dir = "./voice_cloning_outputs"
    os.makedirs(output_dir, exist_ok=True)
    
    # 多人对话文本
    dialog_text = """[SPEAKER0] Hello, how are you doing today?
[SPEAKER1] I'm doing great, thank you for asking! How about you?
[SPEAKER0] I'm wonderful. The weather is really nice today.
[SPEAKER1] Yes, it's perfect for a walk in the park."""
    
    # 声音组合
    voice_combinations = [
        ("belinda", "chadwick"),
        ("en_woman", "en_man"),
        ("mabel", "broom_salesman")
    ]
    
    success_count = 0
    
    for i, (voice1, voice2) in enumerate(voice_combinations):
        output_file = os.path.join(output_dir, f"multi_voice_dialog_{i+1}_{voice1}_{voice2}.wav")
        
        # 构建命令
        cmd = [
            "./conda_env/bin/python", "examples/generation.py",
            "--transcript", dialog_text,
            "--ref_audio", f"{voice1},{voice2}",
            "--ref_audio_in_system_message",
            "--chunk_method", "speaker",
            "--temperature", "0.5",
            "--device", "none",
            "--out_path", output_file
        ]
        
        try:
            logger.info(f"生成多人对话: {voice1} + {voice2}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                logger.info(f"✅ 成功生成: {output_file}")
                success_count += 1
            else:
                logger.error(f"❌ 生成失败: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ 生成超时")
        except Exception as e:
            logger.error(f"💥 生成出错: {e}")
    
    logger.info(f"\n多声音克隆测试完成，成功: {success_count}/{len(voice_combinations)}")
    return success_count > 0

if __name__ == "__main__":
    logger.info("🎭 开始声音克隆测试...")
    
    # 单声音克隆测试
    single_success = test_voice_cloning()
    
    # 多声音克隆测试
    multi_success = test_multi_voice_cloning()
    
    if single_success or multi_success:
        logger.info("🎉 声音克隆测试完成！")
    else:
        logger.error("💥 所有声音克隆测试都失败了！")
        sys.exit(1)
