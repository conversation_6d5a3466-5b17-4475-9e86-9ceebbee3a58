#!/usr/bin/env python3
"""
Higgs Audio V2 语音合成功能全面测试脚本
运行所有测试用例并生成测试报告
"""

import os
import sys
import subprocess
import time
from datetime import datetime
from loguru import logger

def check_environment():
    """检查测试环境"""
    logger.info("🔍 检查测试环境...")
    
    # 检查Python环境
    python_path = "./conda_env/bin/python"
    if not os.path.exists(python_path):
        logger.error(f"❌ Python环境不存在: {python_path}")
        return False
    
    # 检查必要的包
    try:
        result = subprocess.run([python_path, "-c", "import torch, transformers, torchaudio"], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            logger.error(f"❌ 缺少必要的Python包: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"❌ 检查Python包时出错: {e}")
        return False
    
    # 检查模型文件
    model_paths = [
        "./models/hub/bosonai/higgs-audio-v2-generation-3B-base",
        "./models/hub/bosonai/higgs-audio-v2-tokenizer"
    ]
    
    models_available = False
    for path in model_paths:
        if os.path.exists(path):
            logger.info(f"✅ 找到本地模型: {path}")
            models_available = True
        else:
            logger.warning(f"⚠️  本地模型不存在: {path}")
    
    if not models_available:
        logger.warning("⚠️  没有找到本地模型，将尝试从HuggingFace下载")
    
    # 检查示例文件
    if not os.path.exists("examples/generation.py"):
        logger.error("❌ 找不到examples/generation.py")
        return False
    
    logger.info("✅ 环境检查完成")
    return True

def run_test_script(script_name, description):
    """运行单个测试脚本"""
    logger.info(f"\n{'='*50}")
    logger.info(f"🧪 开始测试: {description}")
    logger.info(f"📄 脚本: {script_name}")
    logger.info(f"{'='*50}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(
            ["./conda_env/bin/python", script_name],
            capture_output=True,
            text=True,
            timeout=1800  # 30分钟超时
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            logger.info(f"✅ {description} 测试成功 (耗时: {duration:.1f}秒)")
            logger.info("📋 输出:")
            logger.info(result.stdout)
            return True, duration, result.stdout
        else:
            logger.error(f"❌ {description} 测试失败 (耗时: {duration:.1f}秒)")
            logger.error("📋 错误输出:")
            logger.error(result.stderr)
            return False, duration, result.stderr
            
    except subprocess.TimeoutExpired:
        logger.error(f"⏰ {description} 测试超时")
        return False, 1800, "测试超时"
    except Exception as e:
        logger.error(f"💥 {description} 测试出错: {e}")
        return False, 0, str(e)

def generate_test_report(test_results):
    """生成测试报告"""
    logger.info("\n" + "="*60)
    logger.info("📊 生成测试报告")
    logger.info("="*60)
    
    # 创建报告目录
    report_dir = "./test_reports"
    os.makedirs(report_dir, exist_ok=True)
    
    # 生成报告文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = os.path.join(report_dir, f"higgs_audio_test_report_{timestamp}.md")
    
    # 计算统计信息
    total_tests = len(test_results)
    successful_tests = sum(1 for result in test_results if result['success'])
    failed_tests = total_tests - successful_tests
    total_time = sum(result['duration'] for result in test_results)
    
    # 生成Markdown报告
    report_content = f"""# Higgs Audio V2 语音合成测试报告

**测试时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
**测试环境**: CPU模式 (CUDA不可用)

## 📊 测试概览

- **总测试数**: {total_tests}
- **成功测试**: {successful_tests}
- **失败测试**: {failed_tests}
- **成功率**: {successful_tests/total_tests*100:.1f}%
- **总耗时**: {total_time:.1f}秒 ({total_time/60:.1f}分钟)

## 📋 详细结果

"""
    
    for result in test_results:
        status_icon = "✅" if result['success'] else "❌"
        report_content += f"""### {status_icon} {result['description']}

- **脚本**: `{result['script']}`
- **状态**: {'成功' if result['success'] else '失败'}
- **耗时**: {result['duration']:.1f}秒
- **输出目录**: {result.get('output_dir', 'N/A')}

"""
        if not result['success']:
            report_content += f"""**错误信息**:
```
{result['output'][:500]}...
```

"""
    
    # 添加建议和总结
    report_content += f"""## 🎯 测试总结

"""
    
    if successful_tests == total_tests:
        report_content += """🎉 **所有测试都成功通过！**

Higgs Audio V2语音合成功能工作正常，支持：
- 基础文本转语音
- 声音克隆
- 多人对话生成
- 中文语音合成
- 中英文混合语音

"""
    elif successful_tests > 0:
        report_content += f"""⚠️ **部分测试成功** ({successful_tests}/{total_tests})

成功的功能可以正常使用，失败的测试可能需要：
- 检查模型文件是否完整下载
- 确认网络连接稳定
- 增加GPU支持以提高性能

"""
    else:
        report_content += """❌ **所有测试都失败了**

可能的原因：
- 模型文件未正确下载
- 依赖包版本不兼容
- 系统资源不足

建议：
1. 重新下载模型文件
2. 检查Python环境和依赖
3. 查看详细错误日志

"""
    
    report_content += f"""## 📁 输出文件

测试生成的音频文件保存在以下目录：
- `./basic_tts_outputs/` - 基础语音合成输出
- `./voice_cloning_outputs/` - 声音克隆输出  
- `./chinese_tts_outputs/` - 中文语音合成输出
- `./multi_speaker_outputs/` - 多人对话输出

## 🔧 技术信息

- **Python版本**: 3.10
- **PyTorch版本**: 2.7.1+cu126
- **设备**: CPU (CUDA不可用)
- **模型**: Higgs Audio V2 (3B参数)

---
*报告生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}*
"""
    
    # 保存报告
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    logger.info(f"📄 测试报告已保存: {report_file}")
    
    # 打印简要统计
    logger.info(f"\n📊 测试统计:")
    logger.info(f"成功: {successful_tests}/{total_tests} ({successful_tests/total_tests*100:.1f}%)")
    logger.info(f"总耗时: {total_time:.1f}秒")
    
    return report_file

def main():
    """主测试函数"""
    logger.info("🚀 开始Higgs Audio V2语音合成功能全面测试")
    logger.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查环境
    if not check_environment():
        logger.error("❌ 环境检查失败，无法继续测试")
        sys.exit(1)
    
    # 定义测试用例
    test_cases = [
        ("test_basic_tts.py", "基础语音合成测试"),
        ("test_voice_cloning.py", "声音克隆测试"),
        ("test_chinese_tts.py", "中文语音合成测试"),
        ("test_multi_speaker.py", "多人对话测试")
    ]
    
    # 运行所有测试
    test_results = []
    
    for script, description in test_cases:
        if not os.path.exists(script):
            logger.warning(f"⚠️  测试脚本不存在: {script}")
            continue
            
        success, duration, output = run_test_script(script, description)
        
        test_results.append({
            'script': script,
            'description': description,
            'success': success,
            'duration': duration,
            'output': output
        })
    
    # 生成测试报告
    report_file = generate_test_report(test_results)
    
    # 最终结果
    successful_tests = sum(1 for result in test_results if result['success'])
    total_tests = len(test_results)
    
    logger.info(f"\n🏁 测试完成！")
    logger.info(f"📊 结果: {successful_tests}/{total_tests} 测试成功")
    logger.info(f"📄 详细报告: {report_file}")
    
    if successful_tests == total_tests:
        logger.info("🎉 所有测试都成功通过！")
        return 0
    elif successful_tests > 0:
        logger.warning("⚠️  部分测试成功")
        return 1
    else:
        logger.error("❌ 所有测试都失败了")
        return 2

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
