#!/usr/bin/env python3
"""
中文语音合成测试脚本
测试Higgs Audio V2的中文文本转语音功能
"""

import os
import sys
import subprocess
from loguru import logger

def test_chinese_tts():
    """测试中文语音合成功能"""
    
    # 创建输出目录
    output_dir = "./chinese_tts_outputs"
    os.makedirs(output_dir, exist_ok=True)
    
    # 中文测试文本
    chinese_texts = [
        "你好，这是一个中文语音合成测试。",
        "人工智能正在改变我们的生活和工作方式。",
        "今天天气很好，适合出去散步。",
        "科技发展日新月异，语音合成技术也越来越先进。",
        "欢迎使用Higgs Audio V2语音合成系统。"
    ]
    
    logger.info("开始中文语音合成测试...")
    
    success_count = 0
    
    for i, text in enumerate(chinese_texts):
        logger.info(f"\n正在处理第 {i+1} 个中文文本: {text}")
        
        output_file = os.path.join(output_dir, f"chinese_test_{i+1}.wav")
        
        # 构建命令
        cmd = [
            "./conda_env/bin/python", "examples/generation.py",
            "--transcript", text,
            "--temperature", "0.5",
            "--device", "cuda",
            "--out_path", output_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info(f"✅ 成功生成: {output_file}")
                success_count += 1
            else:
                logger.error(f"❌ 生成失败: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ 生成超时")
        except Exception as e:
            logger.error(f"💥 生成出错: {e}")
    
    # 测试中文声音克隆（如果有中文参考声音）
    if os.path.exists("examples/voice_prompts/zh_man_sichuan.wav"):
        logger.info("\n测试中文声音克隆...")
        
        chinese_clone_text = "这是一个中文声音克隆测试，模型应该生成类似参考声音的中文语音。"
        output_file = os.path.join(output_dir, "chinese_voice_clone.wav")
        
        cmd = [
            "./conda_env/bin/python", "examples/generation.py",
            "--transcript", chinese_clone_text,
            "--ref_audio", "zh_man_sichuan",
            "--temperature", "0.3",
            "--device", "cuda",
            "--out_path", output_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info(f"✅ 中文声音克隆成功: {output_file}")
                success_count += 1
            else:
                logger.error(f"❌ 中文声音克隆失败: {result.stderr}")
                
        except Exception as e:
            logger.error(f"💥 中文声音克隆出错: {e}")
    
    # 测试结果统计
    logger.info(f"\n📊 中文语音合成测试结果:")
    logger.info(f"成功: {success_count}/{len(chinese_texts) + (1 if os.path.exists('examples/voice_prompts/zh_man_sichuan.wav') else 0)}")
    
    if success_count > 0:
        logger.info(f"✅ 中文语音合成测试完成！音频文件保存在: {output_dir}")
        return True
    else:
        logger.error("❌ 所有中文语音合成测试都失败了")
        return False

def test_mixed_language():
    """测试中英文混合语音合成"""
    
    logger.info("\n开始中英文混合语音合成测试...")
    
    output_dir = "./chinese_tts_outputs"
    os.makedirs(output_dir, exist_ok=True)
    
    # 中英文混合文本
    mixed_texts = [
        "Hello，你好！这是一个中英文混合的测试。",
        "我们使用AI技术，也就是Artificial Intelligence来生成语音。",
        "今天的weather很好，适合outdoor activities。"
    ]
    
    success_count = 0
    
    for i, text in enumerate(mixed_texts):
        logger.info(f"处理混合语言文本 {i+1}: {text}")
        
        output_file = os.path.join(output_dir, f"mixed_language_{i+1}.wav")
        
        cmd = [
            "./conda_env/bin/python", "examples/generation.py",
            "--transcript", text,
            "--temperature", "0.4",
            "--device", "cuda",
            "--out_path", output_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info(f"✅ 混合语言生成成功: {output_file}")
                success_count += 1
            else:
                logger.error(f"❌ 混合语言生成失败: {result.stderr}")
                
        except Exception as e:
            logger.error(f"💥 混合语言生成出错: {e}")
    
    logger.info(f"混合语言测试完成，成功: {success_count}/{len(mixed_texts)}")
    return success_count > 0

if __name__ == "__main__":
    logger.info("🇨🇳 开始中文语音合成测试...")
    
    # 中文语音合成测试
    chinese_success = test_chinese_tts()
    
    # 中英文混合测试
    mixed_success = test_mixed_language()
    
    if chinese_success or mixed_success:
        logger.info("🎉 中文语音合成测试完成！")
    else:
        logger.error("💥 所有中文语音合成测试都失败了！")
        sys.exit(1)
