#!/usr/bin/env python3
"""
下载Higgs Audio V2模型文件的脚本
从魔塔社区下载模型文件以获得更快的下载速度
"""

import os
from modelscope import snapshot_download

def download_models():
    """下载Higgs Audio V2所需的模型文件"""
    
    # 创建模型目录
    models_dir = "./models"
    os.makedirs(models_dir, exist_ok=True)
    
    print("开始下载Higgs Audio V2模型文件...")
    
    # 下载生成模型
    # print("\n1. 下载生成模型 (higgs-audio-v2-generation-3B-base)...")
    # try:
    #     generation_model_path = snapshot_download(
    #         'bosonai/higgs-audio-v2-generation-3B-base',
    #         cache_dir=models_dir,
    #         revision='master'
    #     )
    #     print(f"生成模型下载完成，路径: {generation_model_path}")
    # except Exception as e:
    #     print(f"生成模型下载失败: {e}")
    #     return False
    
    # 下载音频tokenizer
    print("\n2. 下载音频tokenizer (higgs-audio-v2-tokenizer)...")
    try:
        tokenizer_path = snapshot_download(
            'bosonai/higgs-audio-v2-tokenizer',
            cache_dir=models_dir,
            revision='master'
        )
        print(f"音频tokenizer下载完成，路径: {tokenizer_path}")
    except Exception as e:
        print(f"音频tokenizer下载失败: {e}")
        return False
    
    print("\n所有模型文件下载完成！")
    print(f"模型文件保存在: {os.path.abspath(models_dir)}")
    
    return True

if __name__ == "__main__":
    success = download_models()
    if success:
        print("\n✅ 模型下载成功！现在可以开始测试语音合成功能了。")
    else:
        print("\n❌ 模型下载失败，请检查网络连接或重试。")
